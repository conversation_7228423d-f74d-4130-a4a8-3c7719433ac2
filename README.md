# Splatter.app Viewer - 架构复原项目

## 项目概述

本项目是对Splatter.app Viewer（3D高斯散点渲染器）的完整架构分析和源码复原。通过对混淆代码的逆向工程，我们重建了这个先进的WebGL2 3D渲染应用的完整架构。

## 🎯 核心特性

- **3D高斯散点渲染**: 实时渲染大规模3D点云数据
- **WebGL2加速**: 充分利用GPU进行高性能渲染
- **流式数据加载**: 支持大型数据集的分块加载
- **多线程处理**: Web Workers并行处理数据
- **响应式UI**: Vue.js 3驱动的现代用户界面
- **移动设备支持**: 触摸手势和移动端优化
- **配置系统**: 支持场景参数的保存和加载

## 🏗️ 架构组件

### 核心模块

1. **Application.js** - 主应用程序类
   - 系统初始化和协调
   - 事件管理和生命周期控制
   - WebGL上下文创建

2. **DataLoader.js** - 数据加载器
   - 元数据获取和解析
   - 分块数据流式加载
   - 缓存和预加载机制

3. **WorkerManager.js** - 工作线程管理
   - Web Workers创建和管理
   - 并行数据处理
   - 线程间通信

### 渲染系统

4. **Renderer.js** - WebGL渲染器
   - 高斯散点渲染算法
   - 着色器管理
   - GPU资源管理

5. **Camera.js** - 相机系统
   - 视图变换和投影
   - 射线投射
   - 视锥体裁剪

6. **Shaders.js** - 着色器程序
   - 顶点和片段着色器
   - 自定义效果支持

### 交互系统

7. **Controls.js** - 用户控制
   - 鼠标和触摸输入
   - 相机轨道控制
   - 手势识别

8. **UIManager.js** - 界面管理
   - Vue.js组件系统
   - 响应式控制面板
   - 移动端适配

### 工具模块

9. **Math.js** - 数学工具
   - 向量和矩阵操作
   - 四元数计算
   - 几何变换

10. **Utils.js** - 通用工具
    - 设备检测
    - 文件处理
    - 性能监控

## 📁 项目结构

```
splatter_app/
├── src/
│   ├── core/
│   │   ├── Application.js      # 主应用类
│   │   ├── DataLoader.js       # 数据加载器
│   │   └── WorkerManager.js    # 工作线程管理
│   ├── rendering/
│   │   ├── Renderer.js         # WebGL渲染器
│   │   ├── Camera.js           # 相机系统
│   │   ├── Shaders.js          # 着色器定义
│   │   └── Utils.js            # 渲染工具
│   ├── controls/
│   │   └── Controls.js         # 用户控制
│   ├── ui/
│   │   └── UIManager.js        # Vue.js UI管理
│   └── utils/
│       ├── Math.js             # 数学工具
│       └── Utils.js            # 通用工具
├── index.html                  # 演示页面
├── ARCHITECTURE.md             # 详细架构文档
├── README.md                   # 项目说明
└── splatter-minified.txt       # 原始混淆代码
```

## 🚀 快速开始

### 1. 查看演示

打开 `index.html` 文件在浏览器中查看UI演示：

```bash
# 使用本地服务器（推荐）
python -m http.server 8000
# 或
npx serve .
```

然后访问 `http://localhost:8000`

### 2. 开发环境

```bash
# 克隆项目
git clone <repository-url>
cd splatter_app

# 安装依赖（如果使用构建工具）
npm install

# 启动开发服务器
npm run dev
```

### 3. 配置模式

在URL中添加 `?configure=token` 参数启用配置模式：
```
http://localhost:8000?configure=your-token
```

## 🔧 技术栈

- **前端框架**: Vue.js 3
- **渲染引擎**: WebGL2
- **数学库**: 自定义向量/矩阵操作
- **并发处理**: Web Workers
- **模块系统**: ES6 Modules
- **构建工具**: 可选（Vite/Webpack）

## 📊 性能特性

- **高帧率渲染**: 60fps流畅体验
- **内存优化**: 智能缓存和垃圾回收
- **GPU加速**: 充分利用WebGL2特性
- **多线程**: 并行数据处理
- **LOD系统**: 距离相关的细节级别

## 🎮 用户交互

### 桌面端
- **鼠标左键**: 旋转视角
- **鼠标右键**: 平移视图
- **滚轮**: 缩放
- **键盘**: 方向键控制

### 移动端
- **单指拖拽**: 旋转视角
- **双指捏合**: 缩放
- **双指拖拽**: 平移视图

## 🔌 API接口

### 数据端点
```
GET /{splatId}/meta          # 获取场景元数据
GET /{splatId}/data/{blockId} # 获取数据块
POST /api/splat/configure    # 保存配置
```

### URL参数
- `id`: 场景ID
- `src`: 数据源路径
- `fov`: 视野角度
- `configure`: 配置模式令牌

## 🛠️ 开发指南

### 添加新功能

1. **渲染效果**: 修改 `Renderer.js` 中的着色器代码
2. **交互控制**: 扩展 `Controls.js` 中的事件处理
3. **UI组件**: 在 `UIManager.js` 中添加Vue组件
4. **数据处理**: 更新 `WorkerManager.js` 中的处理逻辑

### 性能优化

1. **GPU优化**: 使用实例化渲染和批处理
2. **内存管理**: 实现对象池和资源回收
3. **网络优化**: 数据压缩和预加载策略
4. **渲染优化**: 视锥体裁剪和遮挡剔除

## 🐛 调试工具

- **性能监控**: 内置FPS和内存使用监控
- **开发者控制台**: 详细的调试信息输出
- **配置模式**: 实时参数调整和保存

## 📱 浏览器兼容性

- **Chrome**: 56+ (推荐)
- **Firefox**: 51+
- **Safari**: 15+
- **Edge**: 79+

**要求**: WebGL2支持

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

本项目仅用于学习和研究目的。原始Splatter.app代码版权归Jakub Cerveny所有。

## 🙏 致谢

- 感谢Splatter.app的原始开发者Jakub Cerveny
- 感谢3D高斯散点渲染技术的研究者们
- 感谢开源社区的贡献

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 项目Issues: [GitHub Issues]
- 邮箱: [<EMAIL>]

---

**注意**: 这是一个教育性的架构复原项目，用于学习现代Web 3D渲染技术。实际的3D高斯散点渲染需要更复杂的算法实现。
