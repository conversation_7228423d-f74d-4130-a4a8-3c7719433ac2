/**
 * Splatter.app Viewer - Data Loader
 * 负责加载和管理3D散点数据
 */

import { WorkerManager } from './WorkerManager.js';

const DATA_BASE_URL = 'https://data.splatter.app';

export class DataLoader {
    constructor(config, maxConcurrentLoads = 6) {
        // 基础配置
        this.splatId = config.splatId ?? null;
        this.defaultView = config.defaultView ?? [0, 0, 0, 0, 0, 1];
        this.upDirection = config.upDirection ?? null;
        this.backgroundColor = config.backgroundColor ?? [0, 0, 0, 1];
        
        // 数据源URL
        this.baseUrl = '/';
        if (this.splatId) {
            this.baseUrl = `${DATA_BASE_URL}/${this.splatId}`;
        } else {
            const urlParams = new URLSearchParams(document.location.search);
            if (urlParams.has('id')) {
                this.splatId = urlParams.get('id');
                this.baseUrl = `${DATA_BASE_URL}/${this.splatId}`;
            } else if (urlParams.has('src')) {
                this.baseUrl = `/${urlParams.get('src')}`;
            }
        }
        
        // 数据管理
        this.size = 0;
        this.ratio = 1.0;
        this.root = null;
        this.blockSize = 0;
        this.colorMap = [1, 1, 1, 1];
        this.filter2d = 0;
        this.chunkSize = 4096;
        this.chunksPerBlock = 0;
        this.totalChunks = 0;
        this.blockCount = 0;
        
        // 加载管理
        this.loadQueue = [];
        this.loadingSet = new Set();
        this.maxConcurrentLoads = maxConcurrentLoads;
        this.onBlockLoaded = () => {};
        this.loadedBlocks = [];
        this.loadedCount = 0;
        
        // 工作线程管理
        this.workerManager = null;
        
        // 初始化
        this.ready = this.loadMetadata(`${this.baseUrl}/meta`).then(metadata => {
            this.processMetadata(metadata);
        }).then(() => {
            this.workerManager = new WorkerManager(this.chunkSize, this.chunksPerBlock, 4);
        });
    }
    
    /**
     * 加载元数据
     */
    async loadMetadata(url) {
        // 检查是否有全局元数据
        if ('meta' in globalThis) {
            return Promise.resolve(globalThis.meta);
        }
        
        try {
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`Error fetching ${url}: ${response.status} ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            console.error(error);
            throw error;
        }
    }
    
    /**
     * 处理元数据
     */
    processMetadata(metadata) {
        this.size = metadata.size;
        this.ratio = metadata.ratio;
        this.root = {
            size: metadata.root.size,
            radius: metadata.root.radius
        };
        this.blockSize = metadata.block;
        this.colorMap = metadata.colorMap ?? this.colorMap;
        this.filter2d = metadata.filter2d;
        this.upDirection = this.upDirection ?? metadata.up ?? [0, 0, 1];
        
        // 确保颜色映射长度是4的倍数
        while (this.colorMap.length % 4 !== 0) {
            this.colorMap.push(0);
        }
        
        // 计算块和分片信息
        this.blockCount = Math.ceil(this.size / this.blockSize);
        this.chunksPerBlock = Math.floor(this.blockSize / this.chunkSize);
        this.totalChunks = this.blockCount * this.chunksPerBlock;
        this.loadedBlocks = new Uint8Array(this.blockCount);
    }
    
    /**
     * 加载二进制数据
     */
    async loadBinaryData(url) {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`Error fetching ${url}: ${response.status} ${response.statusText}`);
        }
        return response.arrayBuffer();
    }
    
    /**
     * 设置加载队列和回调
     */
    setLoadQueue(queue, callback) {
        this.loadQueue = queue;
        this.onBlockLoaded = callback;
        this.processLoadQueue();
    }
    
    /**
     * 处理加载队列
     */
    processLoadQueue() {
        for (const blockId of this.loadQueue) {
            if (this.loadingSet.size >= this.maxConcurrentLoads) break;
            
            if (!this.loadedBlocks[blockId] && !this.loadingSet.has(blockId)) {
                this.loadingSet.add(blockId);
                this.loadBlock(blockId).then(data => {
                    this.loadingSet.delete(blockId);
                    this.loadedBlocks[blockId] = 1;
                    this.loadedCount++;
                    this.onBlockLoaded(blockId, data);
                    this.processLoadQueue();
                });
            }
        }
    }
    
    /**
     * 加载单个数据块
     */
    async loadBlock(blockId) {
        const url = `${this.baseUrl}/${blockId}`;
        const data = await this.loadBinaryData(url);
        
        // 使用工作线程处理数据
        if (this.workerManager) {
            return this.workerManager.processBlock(data);
        }
        
        return data;
    }
    
    /**
     * 获取加载进度
     */
    getLoadProgress() {
        return {
            loaded: this.loadedCount,
            total: this.blockCount,
            percentage: (this.loadedCount / this.blockCount) * 100
        };
    }
    
    /**
     * 预加载指定的块
     */
    preloadBlocks(blockIds) {
        const newBlocks = blockIds.filter(id => 
            !this.loadedBlocks[id] && !this.loadingSet.has(id)
        );
        
        this.loadQueue = [...new Set([...newBlocks, ...this.loadQueue])];
        this.processLoadQueue();
    }
    
    /**
     * 清理资源
     */
    cleanup() {
        if (this.workerManager) {
            this.workerManager.terminate();
        }
        this.loadQueue = [];
        this.loadingSet.clear();
    }
    
    /**
     * 获取数据统计信息
     */
    getStats() {
        return {
            splatId: this.splatId,
            size: this.size,
            blockSize: this.blockSize,
            blockCount: this.blockCount,
            chunkSize: this.chunkSize,
            chunksPerBlock: this.chunksPerBlock,
            totalChunks: this.totalChunks,
            loadedBlocks: this.loadedCount,
            loadProgress: this.getLoadProgress()
        };
    }
}
