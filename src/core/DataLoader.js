/**
 * Splatter.app Viewer - Data Loader
 * 负责加载和管理3D散点数据，支持大场景分块加载和LOD系统
 */

import { WorkerManager } from './WorkerManager.js';
import { LODManager } from './LODManager.js';
import { SpatialManager } from './SpatialManager.js';
import { vec3 } from '../utils/Math.js';

const DATA_BASE_URL = 'https://data.splatter.app';

export class DataLoader {
    constructor(config, maxConcurrentLoads = 6) {
        // 基础配置
        this.splatId = config.splatId ?? null;
        this.defaultView = config.defaultView ?? [0, 0, 0, 0, 0, 1];
        this.upDirection = config.upDirection ?? null;
        this.backgroundColor = config.backgroundColor ?? [0, 0, 0, 1];

        // 数据源URL
        this.baseUrl = '/';
        if (this.splatId) {
            this.baseUrl = `${DATA_BASE_URL}/${this.splatId}`;
        } else {
            const urlParams = new URLSearchParams(document.location.search);
            if (urlParams.has('id')) {
                this.splatId = urlParams.get('id');
                this.baseUrl = `${DATA_BASE_URL}/${this.splatId}`;
            } else if (urlParams.has('src')) {
                this.baseUrl = `/${urlParams.get('src')}`;
            }
        }

        // 数据管理
        this.size = 0;
        this.ratio = 1.0;
        this.root = null;
        this.blockSize = 0;
        this.colorMap = [1, 1, 1, 1];
        this.filter2d = 0;
        this.chunkSize = 4096;
        this.chunksPerBlock = 0;
        this.totalChunks = 0;
        this.blockCount = 0;

        // 加载管理
        this.loadQueue = [];
        this.loadingSet = new Set();
        this.maxConcurrentLoads = maxConcurrentLoads;
        this.onBlockLoaded = () => {};
        this.onBlockUnloaded = () => {}; // 块卸载回调
        this.loadedBlocks = [];
        this.loadedCount = 0;

        // 管理器实例
        this.lodManager = null;
        this.spatialManager = null;

        // LOD系统（保留向后兼容）
        this.lodLevels = [];
        this.currentLodLevel = 0;
        this.lodDistanceThresholds = [10, 50, 200, 1000];
        this.lodBlockSizes = [256, 512, 1024, 2048];

        // 空间分块系统（保留向后兼容）
        this.spatialTree = null;
        this.blockBounds = new Map();
        this.visibleBlocks = new Set();
        this.priorityQueue = [];

        // 性能监控
        this.loadStats = {
            totalRequests: 0,
            successfulLoads: 0,
            failedLoads: 0,
            averageLoadTime: 0,
            totalLoadTime: 0
        };

        // 工作线程管理
        this.workerManager = null;

        // 初始化
        this.ready = this.loadMetadata(`${this.baseUrl}/meta`).then(metadata => {
            this.processMetadata(metadata);
            this.initializeManagers();
        }).then(() => {
            this.workerManager = new WorkerManager(this.chunkSize, this.chunksPerBlock, 4);
        });
    }

    /**
     * 加载元数据
     */
    async loadMetadata(url) {
        // 检查是否有全局元数据
        if ('meta' in globalThis) {
            return Promise.resolve(globalThis.meta);
        }

        try {
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`Error fetching ${url}: ${response.status} ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    /**
     * 处理元数据
     */
    processMetadata(metadata) {
        this.size = metadata.size;
        this.ratio = metadata.ratio;
        this.root = {
            size: metadata.root.size,
            radius: metadata.root.radius
        };
        this.blockSize = metadata.block;
        this.colorMap = metadata.colorMap ?? this.colorMap;
        this.filter2d = metadata.filter2d;
        this.upDirection = this.upDirection ?? metadata.up ?? [0, 0, 1];

        // 确保颜色映射长度是4的倍数
        while (this.colorMap.length % 4 !== 0) {
            this.colorMap.push(0);
        }

        // 计算块和分片信息
        this.blockCount = Math.ceil(this.size / this.blockSize);
        this.chunksPerBlock = Math.floor(this.blockSize / this.chunkSize);
        this.totalChunks = this.blockCount * this.chunksPerBlock;
        this.loadedBlocks = new Uint8Array(this.blockCount);

        // 初始化LOD系统
        this.initializeLOD(metadata);

        // 处理空间分块信息
        if (metadata.spatialBlocks) {
            this.processSpatialBlocks(metadata.spatialBlocks);
        }
    }

    /**
     * 初始化LOD系统
     */
    initializeLOD(metadata) {
        // 根据场景大小和复杂度计算LOD级别
        const sceneRadius = this.root.radius;
        const splatCount = this.size;

        // 动态计算LOD距离阈值
        this.lodDistanceThresholds = [
            sceneRadius * 0.1,  // 高细节
            sceneRadius * 0.3,  // 中等细节
            sceneRadius * 0.8,  // 低细节
            sceneRadius * 2.0   // 最低细节
        ];

        // 根据散点数量调整块大小
        const baseLodSize = Math.min(this.blockSize, Math.max(256, Math.floor(splatCount / 1000)));
        this.lodBlockSizes = [
            baseLodSize,           // LOD 0: 最高细节
            baseLodSize * 2,       // LOD 1: 高细节
            baseLodSize * 4,       // LOD 2: 中等细节
            baseLodSize * 8        // LOD 3: 低细节
        ];

        // 初始化LOD级别数据
        this.lodLevels = this.lodDistanceThresholds.map((threshold, index) => ({
            level: index,
            distanceThreshold: threshold,
            blockSize: this.lodBlockSizes[index],
            loadedBlocks: new Set(),
            activeBlocks: new Set()
        }));

        console.log('LOD System initialized:', {
            levels: this.lodLevels.length,
            thresholds: this.lodDistanceThresholds,
            blockSizes: this.lodBlockSizes
        });
    }

    /**
     * 加载二进制数据
     */
    async loadBinaryData(url) {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`Error fetching ${url}: ${response.status} ${response.statusText}`);
        }
        return response.arrayBuffer();
    }

    /**
     * 初始化管理器
     */
    initializeManagers() {
        // 初始化LOD管理器
        this.lodManager = new LODManager(
            this.root.radius,
            this.size,
            this.blockSize
        );

        // 初始化空间管理器
        this.spatialManager = new SpatialManager(
            this.root.radius,
            this.blockCount
        );

        // 保持向后兼容性
        this.initializeSpatialStructure();
        this.lodLevels = this.lodManager.lodLevels;
        this.lodDistanceThresholds = this.lodManager.distanceThresholds;
        this.blockBounds = this.spatialManager.blockBounds;
    }

    /**
     * 初始化空间结构（向后兼容）
     */
    initializeSpatialStructure() {
        // 创建八叉树结构用于空间分块
        this.spatialTree = new OctreeNode(
            [-this.root.radius, -this.root.radius, -this.root.radius],
            [this.root.radius, this.root.radius, this.root.radius],
            0, // 深度
            6  // 最大深度
        );

        // 为每个块计算空间边界
        this.calculateBlockBounds();
    }

    /**
     * 处理空间分块信息
     */
    processSpatialBlocks(spatialBlocks) {
        spatialBlocks.forEach((blockInfo, index) => {
            this.blockBounds.set(index, {
                min: blockInfo.min,
                max: blockInfo.max,
                center: [
                    (blockInfo.min[0] + blockInfo.max[0]) * 0.5,
                    (blockInfo.min[1] + blockInfo.max[1]) * 0.5,
                    (blockInfo.min[2] + blockInfo.max[2]) * 0.5
                ],
                radius: vec3.distance(blockInfo.min, blockInfo.max) * 0.5,
                splatCount: blockInfo.count || this.blockSize
            });
        });
    }

    /**
     * 计算块的空间边界
     */
    calculateBlockBounds() {
        const blocksPerAxis = Math.ceil(Math.cbrt(this.blockCount));
        const blockWorldSize = (this.root.radius * 2) / blocksPerAxis;

        for (let i = 0; i < this.blockCount; i++) {
            const x = i % blocksPerAxis;
            const y = Math.floor(i / blocksPerAxis) % blocksPerAxis;
            const z = Math.floor(i / (blocksPerAxis * blocksPerAxis));

            const min = [
                -this.root.radius + x * blockWorldSize,
                -this.root.radius + y * blockWorldSize,
                -this.root.radius + z * blockWorldSize
            ];

            const max = [
                min[0] + blockWorldSize,
                min[1] + blockWorldSize,
                min[2] + blockWorldSize
            ];

            this.blockBounds.set(i, {
                min,
                max,
                center: [
                    (min[0] + max[0]) * 0.5,
                    (min[1] + max[1]) * 0.5,
                    (min[2] + max[2]) * 0.5
                ],
                radius: blockWorldSize * 0.866, // sqrt(3)/2 for cube diagonal
                splatCount: this.blockSize
            });
        }
    }

    /**
     * 更新可见块和LOD级别
     */
    updateVisibility(camera, frustumPlanes, screenSize = { width: 1920, height: 1080 }) {
        // 使用新的空间管理器进行可见性剔除
        const spatialResult = this.spatialManager.updateVisibility(camera, frustumPlanes);
        const visibleBlocks = spatialResult.visibleBlocks;

        const cameraPos = camera.position;
        const lodAssignments = new Map();

        // 清空当前LOD级别的活跃块
        this.lodLevels.forEach(lod => lod.activeBlocks && lod.activeBlocks.clear());

        // 为可见块计算LOD级别
        for (const blockId of visibleBlocks) {
            const bounds = this.blockBounds.get(blockId);
            if (!bounds) continue;

            // 使用LOD管理器计算LOD级别
            const lodLevel = this.lodManager.calculateLODLevel(cameraPos, bounds, screenSize);

            // 检查是否应该加载这个块
            if (this.shouldLoadBlock(blockId, lodLevel, vec3.distance(cameraPos, bounds.center))) {
                lodAssignments.set(blockId, lodLevel);
                if (this.lodLevels[lodLevel] && this.lodLevels[lodLevel].activeBlocks) {
                    this.lodLevels[lodLevel].activeBlocks.add(blockId);
                }
            }
        }

        this.visibleBlocks = visibleBlocks;
        this.updateLoadPriorities(lodAssignments, cameraPos);

        // 执行内存清理
        this.performMemoryCleanup();

        return {
            visibleCount: visibleBlocks.size,
            lodDistribution: this.lodLevels.map(lod => lod.activeBlocks ? lod.activeBlocks.size : 0),
            spatialStats: spatialResult.stats,
            lodStats: this.lodManager.getStats()
        };
    }

    /**
     * 设置加载队列和回调
     */
    setLoadQueue(queue, callback) {
        this.loadQueue = queue;
        this.onBlockLoaded = callback;
        this.processLoadQueue();
    }

    /**
     * 计算LOD级别
     */
    calculateLODLevel(distance, blockRadius) {
        // 考虑块的大小调整距离阈值
        const adjustedDistance = distance / Math.max(1, blockRadius / this.root.radius);

        for (let i = 0; i < this.lodDistanceThresholds.length; i++) {
            if (adjustedDistance <= this.lodDistanceThresholds[i]) {
                return i;
            }
        }

        return this.lodDistanceThresholds.length - 1; // 最低LOD级别
    }

    /**
     * 检查块是否在视锥体内
     */
    isBlockInFrustum(bounds, frustumPlanes) {
        const center = bounds.center;
        const radius = bounds.radius;

        // 对每个视锥体平面进行测试
        for (const plane of frustumPlanes) {
            const distance = plane[0] * center[0] + plane[1] * center[1] + plane[2] * center[2] + plane[3];
            if (distance < -radius) {
                return false; // 完全在平面外侧
            }
        }

        return true;
    }

    /**
     * 判断是否应该加载块
     */
    shouldLoadBlock(blockId, lodLevel, distance) {
        // 检查是否已经在更高LOD级别加载
        for (let i = 0; i < lodLevel; i++) {
            if (this.lodLevels[i].loadedBlocks.has(blockId)) {
                return false; // 已有更高质量版本
            }
        }

        // 检查内存使用情况
        if (this.getMemoryUsage() > this.getMemoryLimit()) {
            return distance < this.lodDistanceThresholds[0]; // 只加载最近的块
        }

        return true;
    }

    /**
     * 更新加载优先级
     */
    updateLoadPriorities(lodAssignments, cameraPos) {
        this.priorityQueue = [];

        for (const [blockId, lodLevel] of lodAssignments) {
            if (!this.loadedBlocks[blockId] && !this.loadingSet.has(blockId)) {
                const bounds = this.blockBounds.get(blockId);
                const distance = vec3.distance(cameraPos, bounds.center);

                // 计算优先级分数（距离越近，LOD级别越高，优先级越高）
                const priority = this.calculatePriority(distance, lodLevel, bounds);

                this.priorityQueue.push({
                    blockId,
                    lodLevel,
                    distance,
                    priority,
                    bounds
                });
            }
        }

        // 按优先级排序
        this.priorityQueue.sort((a, b) => b.priority - a.priority);

        // 更新加载队列
        this.loadQueue = this.priorityQueue.slice(0, this.maxConcurrentLoads * 2).map(item => item.blockId);
    }

    /**
     * 计算块的加载优先级
     */
    calculatePriority(distance, lodLevel, bounds) {
        // 基础优先级：距离越近优先级越高
        let priority = 1000 / (distance + 1);

        // LOD级别调整：高LOD级别优先级更高
        priority *= (this.lodLevels.length - lodLevel + 1);

        // 块大小调整：包含更多散点的块优先级更高
        priority *= Math.log(bounds.splatCount + 1);

        // 视角中心调整：靠近视角中心的块优先级更高
        const screenImportance = this.calculateScreenImportance(bounds);
        priority *= screenImportance;

        return priority;
    }

    /**
     * 计算块在屏幕上的重要性
     */
    calculateScreenImportance(bounds) {
        // 简化实现：假设视角中心重要性最高
        const centerDistance = vec3.distance([0, 0, 0], bounds.center);
        return 1 / (centerDistance / this.root.radius + 0.1);
    }

    /**
     * 处理加载队列
     */
    processLoadQueue() {
        for (const blockId of this.loadQueue) {
            if (this.loadingSet.size >= this.maxConcurrentLoads) break;

            if (!this.loadedBlocks[blockId] && !this.loadingSet.has(blockId)) {
                this.loadingSet.add(blockId);

                const startTime = performance.now();
                this.loadStats.totalRequests++;

                this.loadBlock(blockId).then(data => {
                    const loadTime = performance.now() - startTime;
                    this.updateLoadStats(loadTime, true);

                    this.loadingSet.delete(blockId);
                    this.loadedBlocks[blockId] = 1;
                    this.loadedCount++;

                    // 标记对应LOD级别已加载
                    const lodLevel = this.getCurrentLODLevel(blockId);
                    if (lodLevel !== -1) {
                        this.lodLevels[lodLevel].loadedBlocks.add(blockId);
                    }

                    this.onBlockLoaded(blockId, data);
                    this.processLoadQueue();
                }).catch(error => {
                    const loadTime = performance.now() - startTime;
                    this.updateLoadStats(loadTime, false);

                    this.loadingSet.delete(blockId);
                    console.error(`Failed to load block ${blockId}:`, error);

                    // 重试机制
                    setTimeout(() => {
                        if (this.visibleBlocks.has(blockId)) {
                            this.processLoadQueue();
                        }
                    }, 1000);
                });
            }
        }
    }

    /**
     * 加载单个数据块
     */
    async loadBlock(blockId) {
        const url = `${this.baseUrl}/${blockId}`;
        const data = await this.loadBinaryData(url);

        // 使用工作线程处理数据
        if (this.workerManager) {
            return this.workerManager.processBlock(data);
        }

        return data;
    }

    /**
     * 获取加载进度
     */
    getLoadProgress() {
        return {
            loaded: this.loadedCount,
            total: this.blockCount,
            percentage: (this.loadedCount / this.blockCount) * 100
        };
    }

    /**
     * 预加载指定的块
     */
    preloadBlocks(blockIds) {
        const newBlocks = blockIds.filter(id =>
            !this.loadedBlocks[id] && !this.loadingSet.has(id)
        );

        this.loadQueue = [...new Set([...newBlocks, ...this.loadQueue])];
        this.processLoadQueue();
    }

    /**
     * 清理资源
     */
    cleanup() {
        if (this.workerManager) {
            this.workerManager.terminate();
        }
        this.loadQueue = [];
        this.loadingSet.clear();
    }

    /**
     * 获取当前块的LOD级别
     */
    getCurrentLODLevel(blockId) {
        for (let i = 0; i < this.lodLevels.length; i++) {
            if (this.lodLevels[i].activeBlocks.has(blockId)) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 更新加载统计信息
     */
    updateLoadStats(loadTime, success) {
        if (success) {
            this.loadStats.successfulLoads++;
        } else {
            this.loadStats.failedLoads++;
        }

        this.loadStats.totalLoadTime += loadTime;
        this.loadStats.averageLoadTime = this.loadStats.totalLoadTime / this.loadStats.totalRequests;
    }

    /**
     * 获取内存使用情况（MB）
     */
    getMemoryUsage() {
        if (performance.memory) {
            return performance.memory.usedJSHeapSize / (1024 * 1024);
        }

        // 估算内存使用：每个散点约32字节
        const estimatedSplats = this.loadedCount * this.blockSize;
        return (estimatedSplats * 32) / (1024 * 1024);
    }

    /**
     * 获取内存限制（MB）
     */
    getMemoryLimit() {
        // 根据设备类型设置不同的内存限制
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

        if (performance.memory) {
            // 使用总内存的60%作为限制
            return (performance.memory.totalJSHeapSize * 0.6) / (1024 * 1024);
        }

        // 默认限制
        return isMobile ? 512 : 2048; // MB
    }

    /**
     * 内存清理
     */
    performMemoryCleanup() {
        const memoryUsage = this.getMemoryUsage();
        const memoryLimit = this.getMemoryLimit();

        if (memoryUsage > memoryLimit * 0.8) {
            console.log('Performing memory cleanup...');

            // 卸载最远的低优先级块
            const blocksToUnload = this.selectBlocksForUnloading();

            blocksToUnload.forEach(blockId => {
                this.unloadBlock(blockId);
            });

            // 强制垃圾回收（如果可用）
            if (window.gc) {
                window.gc();
            }
        }
    }

    /**
     * 选择要卸载的块
     */
    selectBlocksForUnloading() {
        const unloadCandidates = [];

        // 收集不在当前视野中的已加载块
        for (let i = 0; i < this.blockCount; i++) {
            if (this.loadedBlocks[i] && !this.visibleBlocks.has(i)) {
                const bounds = this.blockBounds.get(i);
                if (bounds) {
                    unloadCandidates.push({
                        blockId: i,
                        lastAccessTime: Date.now(), // 简化实现
                        memorySize: this.estimateBlockMemorySize(i)
                    });
                }
            }
        }

        // 按最后访问时间排序，优先卸载最久未访问的
        unloadCandidates.sort((a, b) => a.lastAccessTime - b.lastAccessTime);

        // 返回需要卸载的块ID列表
        return unloadCandidates.slice(0, Math.ceil(unloadCandidates.length * 0.3)).map(item => item.blockId);
    }

    /**
     * 卸载块
     */
    unloadBlock(blockId) {
        if (this.loadedBlocks[blockId]) {
            this.loadedBlocks[blockId] = 0;
            this.loadedCount--;

            // 从所有LOD级别中移除
            this.lodLevels.forEach(lod => {
                lod.loadedBlocks.delete(blockId);
                lod.activeBlocks.delete(blockId);
            });

            // 通知渲染器释放GPU资源
            if (this.onBlockUnloaded) {
                this.onBlockUnloaded(blockId);
            }
        }
    }

    /**
     * 估算块的内存大小
     */
    estimateBlockMemorySize(blockId) {
        const bounds = this.blockBounds.get(blockId);
        if (bounds) {
            return (bounds.splatCount * 32) / (1024 * 1024); // MB
        }
        return (this.blockSize * 32) / (1024 * 1024); // MB
    }

    /**
     * 获取LOD统计信息
     */
    getLODStats() {
        return {
            currentLodLevel: this.currentLodLevel,
            lodLevels: this.lodLevels.map((lod, index) => ({
                level: index,
                distanceThreshold: lod.distanceThreshold,
                blockSize: lod.blockSize,
                loadedBlocks: lod.loadedBlocks.size,
                activeBlocks: lod.activeBlocks.size
            })),
            visibleBlocks: this.visibleBlocks.size,
            memoryUsage: this.getMemoryUsage(),
            memoryLimit: this.getMemoryLimit()
        };
    }

    /**
     * 更新性能指标
     */
    updatePerformanceMetrics(frameTime, memoryUsage, renderTime) {
        if (this.lodManager) {
            this.lodManager.updatePerformanceMetrics(frameTime, memoryUsage, renderTime);
        }
    }

    /**
     * 设置LOD偏移
     */
    setLODBias(bias) {
        if (this.lodManager) {
            this.lodManager.setLODBias(bias);
        }
    }

    /**
     * 启用/禁用自适应LOD
     */
    setAdaptiveLOD(enabled) {
        if (this.lodManager) {
            this.lodManager.setAdaptiveLOD(enabled);
        }
    }

    /**
     * 设置性能目标FPS
     */
    setPerformanceTarget(targetFPS) {
        if (this.lodManager) {
            this.lodManager.setPerformanceTarget(targetFPS);
        }
    }

    /**
     * 获取推荐的渲染设置
     */
    getRecommendedSettings() {
        if (this.lodManager) {
            return this.lodManager.getRecommendedSettings();
        }

        return {
            maxConcurrentLoads: 6,
            enableShadows: true,
            enableAntialiasing: true,
            renderScale: 1.0,
            lodBias: 0
        };
    }

    /**
     * 获取需要加载的块
     */
    getBlocksToLoad(cameraPos, loadRadius = null) {
        if (this.spatialManager) {
            return this.spatialManager.getBlocksToLoad(cameraPos, loadRadius);
        }

        // 回退到简单实现
        const blocksToLoad = new Set();
        const radius = loadRadius || this.root.radius * 1.5;

        for (const [blockId, bounds] of this.blockBounds) {
            const distance = vec3.distance(cameraPos, bounds.center);
            if (distance <= radius + bounds.radius) {
                blocksToLoad.add(blockId);
            }
        }

        return blocksToLoad;
    }

    /**
     * 获取需要卸载的块
     */
    getBlocksToUnload(cameraPos, unloadRadius = null) {
        if (this.spatialManager) {
            return this.spatialManager.getBlocksToUnload(cameraPos, unloadRadius);
        }

        // 回退到简单实现
        const blocksToUnload = new Set();
        const radius = unloadRadius || this.root.radius * 3.0;

        for (const [blockId, bounds] of this.blockBounds) {
            const distance = vec3.distance(cameraPos, bounds.center);
            if (distance > radius + bounds.radius) {
                blocksToUnload.add(blockId);
            }
        }

        return blocksToUnload;
    }

    /**
     * 获取块的邻居
     */
    getNeighborBlocks(blockId, radius = 1) {
        if (this.spatialManager) {
            return this.spatialManager.getNeighborBlocks(blockId, radius);
        }

        // 回退到简单实现
        const bounds = this.blockBounds.get(blockId);
        if (!bounds) return new Set();

        const neighbors = new Set();
        const searchRadius = bounds.radius * radius;

        for (const [otherId, otherBounds] of this.blockBounds) {
            if (otherId === blockId) continue;

            const distance = vec3.distance(bounds.center, otherBounds.center);
            if (distance <= searchRadius + otherBounds.radius) {
                neighbors.add(otherId);
            }
        }

        return neighbors;
    }

    /**
     * 获取数据统计信息
     */
    getStats() {
        const baseStats = {
            splatId: this.splatId,
            size: this.size,
            blockSize: this.blockSize,
            blockCount: this.blockCount,
            chunkSize: this.chunkSize,
            chunksPerBlock: this.chunksPerBlock,
            totalChunks: this.totalChunks,
            loadedBlocks: this.loadedCount,
            loadProgress: this.getLoadProgress(),
            loadStats: this.loadStats
        };

        // 添加LOD和空间管理统计
        if (this.lodManager) {
            baseStats.lodStats = this.lodManager.getStats();
        }

        if (this.spatialManager) {
            baseStats.spatialStats = this.spatialManager.getStats();
        }

        return baseStats;
    }
}

/**
 * 八叉树节点类 - 用于空间分块管理
 */
class OctreeNode {
    constructor(min, max, depth = 0, maxDepth = 6) {
        this.min = min;
        this.max = max;
        this.center = [
            (min[0] + max[0]) * 0.5,
            (min[1] + max[1]) * 0.5,
            (min[2] + max[2]) * 0.5
        ];
        this.size = [
            max[0] - min[0],
            max[1] - min[1],
            max[2] - min[2]
        ];
        this.depth = depth;
        this.maxDepth = maxDepth;
        this.children = null;
        this.blocks = new Set(); // 包含的块ID
        this.isLeaf = depth >= maxDepth;
    }

    /**
     * 细分节点
     */
    subdivide() {
        if (this.children || this.isLeaf) return;

        this.children = [];
        const halfSize = [
            this.size[0] * 0.5,
            this.size[1] * 0.5,
            this.size[2] * 0.5
        ];

        // 创建8个子节点
        for (let i = 0; i < 8; i++) {
            const x = (i & 1) ? 1 : 0;
            const y = (i & 2) ? 1 : 0;
            const z = (i & 4) ? 1 : 0;

            const childMin = [
                this.min[0] + x * halfSize[0],
                this.min[1] + y * halfSize[1],
                this.min[2] + z * halfSize[2]
            ];

            const childMax = [
                childMin[0] + halfSize[0],
                childMin[1] + halfSize[1],
                childMin[2] + halfSize[2]
            ];

            this.children.push(new OctreeNode(
                childMin,
                childMax,
                this.depth + 1,
                this.maxDepth
            ));
        }
    }

    /**
     * 插入块
     */
    insertBlock(blockId, bounds) {
        // 检查块是否与节点相交
        if (!this.intersectsBounds(bounds)) {
            return false;
        }

        if (this.isLeaf) {
            this.blocks.add(blockId);
            return true;
        }

        // 如果没有子节点，创建它们
        if (!this.children) {
            this.subdivide();
        }

        // 尝试插入到子节点
        let inserted = false;
        for (const child of this.children) {
            if (child.insertBlock(blockId, bounds)) {
                inserted = true;
            }
        }

        // 如果无法插入到任何子节点，保存在当前节点
        if (!inserted) {
            this.blocks.add(blockId);
        }

        return true;
    }

    /**
     * 查询与视锥体相交的块
     */
    queryFrustum(frustumPlanes, result = new Set()) {
        // 检查节点是否与视锥体相交
        if (!this.intersectsFrustum(frustumPlanes)) {
            return result;
        }

        // 添加当前节点的块
        for (const blockId of this.blocks) {
            result.add(blockId);
        }

        // 递归查询子节点
        if (this.children) {
            for (const child of this.children) {
                child.queryFrustum(frustumPlanes, result);
            }
        }

        return result;
    }

    /**
     * 检查是否与边界框相交
     */
    intersectsBounds(bounds) {
        return !(
            bounds.max[0] < this.min[0] || bounds.min[0] > this.max[0] ||
            bounds.max[1] < this.min[1] || bounds.min[1] > this.max[1] ||
            bounds.max[2] < this.min[2] || bounds.min[2] > this.max[2]
        );
    }

    /**
     * 检查是否与视锥体相交
     */
    intersectsFrustum(frustumPlanes) {
        // 简化的视锥体-AABB相交测试
        for (const plane of frustumPlanes) {
            // 计算AABB的正顶点（相对于平面法向量）
            const positive = [
                plane[0] >= 0 ? this.max[0] : this.min[0],
                plane[1] >= 0 ? this.max[1] : this.min[1],
                plane[2] >= 0 ? this.max[2] : this.min[2]
            ];

            // 如果正顶点在平面外侧，则AABB完全在平面外侧
            const distance = plane[0] * positive[0] + plane[1] * positive[1] + plane[2] * positive[2] + plane[3];
            if (distance < 0) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取节点统计信息
     */
    getStats() {
        let nodeCount = 1;
        let leafCount = this.isLeaf ? 1 : 0;
        let blockCount = this.blocks.size;

        if (this.children) {
            for (const child of this.children) {
                const childStats = child.getStats();
                nodeCount += childStats.nodeCount;
                leafCount += childStats.leafCount;
                blockCount += childStats.blockCount;
            }
        }

        return { nodeCount, leafCount, blockCount };
    }
}
